import pandas as pd
import json
import os
from config.logging import get_logger
from config.llm_config import get_llm
from models.models import SchemaDefinition
from pydantic import ValidationError


class RuleExtractionAgent:
    def __init__(self, prompt_path=r"ContextGuardrail\rule_extraction_prompts.txt"):
        self.llm = get_llm()
        self.logger = get_logger("RuleExtractionAgent", "logs/rule_extraction.log")
        self.prompt_template = self._load_prompt_template(prompt_path)

    def _load_prompt_template(self, prompt_path):
        try:
            with open(prompt_path, "r", encoding="utf-8") as file:
                return file.read()
        except Exception as e:
            self.logger.error(f"[RuleAgent] Failed to load prompt template: {e}")
            raise

    def run(self, metadata_excel_path: str) -> dict:
        self.logger.info(f"[RuleAgent] Reading metadata file: {metadata_excel_path}")

        try:
            df = pd.read_excel(metadata_excel_path)
            self.logger.info(f"[RuleAgent]  Excel loaded with {df.shape[0]} rows and {df.shape[1]} columns.")
        except Exception as e:
            self.logger.error(f"[RuleAgent]  Failed to read metadata file: {e}")
            return {"success": False, "message": str(e), "schema": []}

        df.columns = df.columns.str.strip().str.upper()
        if "FILE_COLUMN_NAME" not in df.columns:
            msg = "'FILE_COLUMN_NAME' column is required in metadata file"
            self.logger.error(f"[RuleAgent]  {msg}")
            return {"success": False, "message": msg, "schema": []}

        excel_columns = df["FILE_COLUMN_NAME"].dropna().astype(str).tolist()
        self.logger.info(f"[RuleAgent]  Extracted columns: {excel_columns}")

        prompt = self.prompt_template.format(
        column_list=json.dumps(excel_columns, indent=2))
        self.logger.info("[RuleAgent]  Invoking LLM for schema extraction...")
        self.logger.debug(f"[RuleAgent]  LLM PROMPT:\n{prompt}")
        response = self.llm(prompt)

        try:
            self.logger.info("[RuleAgent]  Invoking LLM for schema extraction...")
            self.logger.debug(f"[RuleAgent]  LLM PROMPT:\n{prompt}")
            response = self.llm(prompt)
            self.logger.debug(f"[RuleAgent]  LLM RAW RESPONSE:\n{response}")
            clean_response = response.strip().strip("```json").strip("```")
            schema_dict = json.loads(clean_response)
        except Exception as e:
            self.logger.error(f"[RuleAgent]  Failed to parse LLM response: {e}")
            return {
                "success": False,
                "message": f"LLM extraction failed: {e}",
                "schema": []
            }

        try:
            validated_schema = SchemaDefinition(**schema_dict)
            self.logger.info(f"[RuleAgent]  Extracted {len(validated_schema.columns)} schema columns from LLM.")
        except ValidationError as e:
            self.logger.error(f"[RuleAgent]  Pydantic validation failed:\n{e}")
            return {
                "success": False,
                "message": "Pydantic validation failed",
                "schema": []
            }

        try:
            self.logger.info("[RuleAgent]  Saving schema to file...")
            os.makedirs("data", exist_ok=True)
            schema_output_path = "data/outputs/xls_validation_rules.json"
            with open(schema_output_path, "w", encoding="utf-8") as f:
                json.dump({"schema_definition": validated_schema.dict()}, f, indent=2)
            self.logger.info(f"[RuleAgent]  Schema saved to: {schema_output_path}")
        except Exception as e:
            self.logger.error(f"[RuleAgent]  Failed to save schema: {e}")
            return {
                "success": False,
                "message": "Schema extracted but failed to save.",
                "schema": validated_schema.dict()
            }

        return {
            "success": True,
            "message": "Schema extracted and saved successfully.",
            "schema": validated_schema.dict(),
            "schema_path": schema_output_path
        }
