{"timestamp": "20250715_145853", "file_path": "data\\inputs\\Fundholding_Data _2 1.xlsx", "schema_path": "data/outputs/xls_validation_rules.json", "validation_result": {"success": true, "message": "The uploaded Excel file contains the following columns:\n\n- UNIQUE_ID\n- P<PERSON><PERSON>OLIO_ID\n- REGISTERED_HOLDER\n- NAV\n- OWNERSHIP_PERCENTAGE\n- CAPITAL_CALLED\n- NO_OF_SHARES\n- COMMITTED_CAPITAL\n- PERIOD\n- FUND_NAME\n\n**Comparison with expected schema:**\n\n1. **Columns Present:**  \n   All expected columns are present in the file.\n\n2. **Column Order:**  \n   The order matches the expected 'position' sequence.\n\n3. **Data Types (based on sample data):**  \n   - UNIQUE_ID: STRING (e.g., \"UID_8270\")  \n   - PORTFOLIO_ID: STRING (e.g., \"PORT_646\")  \n   - REGISTERED_HOLDER: STRING (e.g., \"Holder_56\")  \n   - NAV: NUMBER (e.g., 361352.84)  \n   - OWNERSHIP_PERCENTAGE: NUMBER (e.g., 4)  \n   - CAPITAL_CALLED: NUMBER (e.g., 94555.55)  \n   - NO_OF_SHARES: NUMBER (e.g., 4264)  \n   - COMMITTED_CAPITAL: NUMBER (e.g., 191240.14)  \n   - PERIOD: STRING (e.g., \"Q3-2023\")  \n   - FUND_NAME: STRING (e.g., \"Alpha Fund\")  \n\n4. **Required and Nullable Fields:**  \n   - All columns are marked as required and non-nullable.  \n   - Sample data shows no nulls in these fields.\n\n**Assessment:**\n\n- No missing columns.  \n- No extra columns.  \n- Column order is correct.  \n- Data types are consistent with expectations.  \n- All required fields are present and non-null in sample data.\n\n**Verdict:**  \n**VALID** — The uploaded file adheres to the predefined schema and data integrity requirements."}, "actual_columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "sample_data": [{"UNIQUE_ID": "UID_8270", "PORTFOLIO_ID": "PORT_646", "REGISTERED_HOLDER": "Holder_56", "NAV": 361352.84, "OWNERSHIP_PERCENTAGE": 4, "CAPITAL_CALLED": 94555.55, "NO_OF_SHARES": 4264, "COMMITTED_CAPITAL": 191240.14, "PERIOD": "Q3-2023", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_1860", "PORTFOLIO_ID": "PORT_838", "REGISTERED_HOLDER": "Holder_81", "NAV": 343382.57, "OWNERSHIP_PERCENTAGE": 5, "CAPITAL_CALLED": 124852.64, "NO_OF_SHARES": 8041, "COMMITTED_CAPITAL": 175229.36, "PERIOD": "Q2-2024", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_6390", "PORTFOLIO_ID": "PORT_712", "REGISTERED_HOLDER": "Holder_59", "NAV": 150921.19, "OWNERSHIP_PERCENTAGE": 3, "CAPITAL_CALLED": 398374.24, "NO_OF_SHARES": 5669, "COMMITTED_CAPITAL": 459640.88, "PERIOD": "Q3-2025", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_6191", "PORTFOLIO_ID": "PORT_561", "REGISTERED_HOLDER": "Holder_2", "NAV": 370480.65, "OWNERSHIP_PERCENTAGE": 7, "CAPITAL_CALLED": 281800.77, "NO_OF_SHARES": 4400, "COMMITTED_CAPITAL": 350068.61, "PERIOD": "Q1-2024", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_6734", "PORTFOLIO_ID": "PORT_742", "REGISTERED_HOLDER": "Holder_2", "NAV": 156762.09, "OWNERSHIP_PERCENTAGE": 5, "CAPITAL_CALLED": 159838.71, "NO_OF_SHARES": 6369, "COMMITTED_CAPITAL": 234229.2, "PERIOD": "Q2-2024", "FUND_NAME": "Alpha Fund"}]}