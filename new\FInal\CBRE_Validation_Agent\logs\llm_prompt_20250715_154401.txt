# Placeholder for ContextGuardrail/file_validation_prompts.txt

You are a file validation agent.

You will validate the uploaded Excel file based on:
1. Predefined schema (columns, data types, required fields, nullable)
2. Actual columns in the uploaded file
3. Sample preview of the data

Expected Schema:
{
  "columns": [
    {
      "column_name": "UNIQUE_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    {
      "column_name": "PORTFOLIO_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 2
    },
    {
      "column_name": "REGISTERED_HOLDER",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 3
    },
    {
      "column_name": "NAV",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 4
    },
    {
      "column_name": "OWNERSHIP_PERCENTAGE",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 5
    },
    {
      "column_name": "CAPITAL_CALLED",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 6
    },
    {
      "column_name": "NO_OF_SHARES",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 7
    },
    {
      "column_name": "COMMITTED_CAPITAL",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 8
    },
    {
      "column_name": "PERIOD",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 9
    },
    {
      "column_name": "FUND_NAME",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 10
    }
  ]
}

Actual Columns in File:
[
  "UNIQUE_ID",
  "PORTFOLIO_ID",
  "REGISTERED_HOLDER",
  "NAV",
  "OWNERSHIP_PERCENTAGE",
  "CAPITAL_CALLED",
  "NO_OF_SHARES",
  "COMMITTED_CAPITAL",
  "PERIOD",
  "FUND_NAME"
]

Sample Data:
[
  {
    "UNIQUE_ID": "UID_8637",
    "PORTFOLIO_ID": "PORT_982",
    "REGISTERED_HOLDER": "Holder_1",
    "NAV": 420278.83,
    "OWNERSHIP_PERCENTAGE": 57.98,
    "CAPITAL_CALLED": 36688.6,
    "NO_OF_SHARES": 2891.0,
    "COMMITTED_CAPITAL": 444628.33,
    "PERIOD": "Q3-2025",
    "FUND_NAME": "Beta Fund"
  },
  {
    "UNIQUE_ID": "UID_9449",
    "PORTFOLIO_ID": "PORT_739",
    "REGISTERED_HOLDER": "Holder_64",
    "NAV": 408952.82,
    "OWNERSHIP_PERCENTAGE": 74.61,
    "CAPITAL_CALLED": 168681.11,
    "NO_OF_SHARES": 639.0,
    "COMMITTED_CAPITAL": 168343.59,
    "PERIOD": "Q1-2025",
    "FUND_NAME": "Delta Fund"
  },
  {
    "UNIQUE_ID": "UID_2072",
    "PORTFOLIO_ID": "PORT_533",
    "REGISTERED_HOLDER": "Holder_44",
    "NAV": 96863.47,
    "OWNERSHIP_PERCENTAGE": null,
    "CAPITAL_CALLED": 297529.46,
    "NO_OF_SHARES": 7199.0,
    "COMMITTED_CAPITAL": 495606.56,
    "PERIOD": "Q3-2025",
    "FUND_NAME": "Beta Fund"
  },
  {
    "UNIQUE_ID": "UID_8735",
    "PORTFOLIO_ID": "PORT_293",
    "REGISTERED_HOLDER": "Holder_20",
    "NAV": 191019.27,
    "OWNERSHIP_PERCENTAGE": 5.55,
    "CAPITAL_CALLED": 167299.91,
    "NO_OF_SHARES": 6076.0,
    "COMMITTED_CAPITAL": 470566.79,
    "PERIOD": "Q2-2025",
    "FUND_NAME": "Delta Fund"
  },
  {
    "UNIQUE_ID": "UID_4088",
    "PORTFOLIO_ID": "PORT_999",
    "REGISTERED_HOLDER": "Holder_9",
    "NAV": 64727.77,
    "OWNERSHIP_PERCENTAGE": 92.5,
    "CAPITAL_CALLED": 108966.1,
    "NO_OF_SHARES": 6203.0,
    "COMMITTED_CAPITAL": 236765.15,
    "PERIOD": "Q2-2025",
    "FUND_NAME": "Gamma Fund"
  }
]

Instructions:
- Highlight missing or extra columns.
- Ensure column order matches expected 'position'.
- Validate types (STRING vs NUMBER).
- Strictly enforce non-null and required fields.
- Do not assume anything beyond this input.

End with a clear verdict:
- VALID (if file structure is acceptable)
- INVALID (if any issues found)
