2025-07-15 10:48:18,876 - INFO - SPExtractorAgent initialized.
2025-07-15 10:48:36,717 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 10:48:36,719 - INFO - Stored procedure file loaded successfully.
2025-07-15 10:48:36,721 - ERROR - ❌ SP Extraction failed: 'charmap' codec can't decode byte 0x90 in position 22: character maps to <undefined>
2025-07-15 10:53:25,393 - INFO - SPExtractorAgent initialized.
2025-07-15 10:53:38,559 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 10:53:38,561 - INFO - Stored procedure file loaded successfully.
2025-07-15 10:53:38,563 - ERROR - ❌ SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 10:55:34,268 - INFO - SPExtractorAgent initialized.
2025-07-15 10:55:47,008 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 10:55:47,124 - INFO - Stored procedure file loaded successfully.
2025-07-15 10:55:47,126 - ERROR - ❌ SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 11:04:56,993 - INFO - SPExtractorAgent initialized.
2025-07-15 11:05:05,324 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 11:05:05,350 - INFO - Stored procedure file loaded successfully.
2025-07-15 11:05:05,350 - ERROR - ❌ SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 11:06:51,954 - INFO - SPExtractorAgent initialized.
2025-07-15 11:06:58,383 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 11:06:58,389 - INFO - Stored procedure file loaded successfully.
2025-07-15 11:06:58,390 - ERROR - ❌ SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 11:20:22,100 - INFO - SPExtractorAgent initialized.
2025-07-15 11:20:30,133 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 11:20:30,139 - INFO - Stored procedure file loaded successfully.
2025-07-15 11:20:30,140 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 11:29:44,578 - INFO - SPExtractorAgent initialized.
2025-07-15 11:31:42,088 - INFO - SPExtractorAgent initialized.
2025-07-15 11:31:54,362 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 11:31:54,370 - INFO - Stored procedure file loaded successfully.
2025-07-15 11:31:54,372 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 11:34:06,826 - INFO - SPExtractorAgent initialized.
2025-07-15 11:34:21,118 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 11:34:21,128 - INFO - Stored procedure file loaded successfully.
2025-07-15 11:34:21,129 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 11:49:04,347 - INFO - SPExtractorAgent initialized.
2025-07-15 11:49:17,893 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 11:49:17,899 - INFO - Stored procedure file loaded successfully.
2025-07-15 11:49:17,900 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 13:04:21,295 - INFO - SPExtractorAgent initialized.
2025-07-15 13:04:33,366 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 13:04:33,377 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:04:33,379 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 13:09:28,270 - INFO - SPExtractorAgent initialized.
2025-07-15 13:09:40,193 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 13:09:40,195 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:09:40,195 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 13:10:37,575 - INFO - SPExtractorAgent initialized.
2025-07-15 13:10:49,528 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 13:10:49,528 - ERROR -  SP Extraction failed: [Errno 2] No such file or directory: 'data\\inputs\\stored_procedures.json'
2025-07-15 13:20:06,246 - INFO - SPExtractorAgent initialized.
2025-07-15 13:20:17,228 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 13:20:17,228 - ERROR -  SP Extraction failed: [Errno 2] No such file or directory: 'data\\inputs\\stored_procedures.json'
2025-07-15 13:31:38,633 - INFO - SPExtractorAgent initialized.
2025-07-15 13:31:51,166 - INFO - Running SP extraction for file: data\inputs\stored_procedures.json
2025-07-15 13:31:51,166 - ERROR -  SP Extraction failed: [Errno 2] No such file or directory: 'data\\inputs\\stored_procedures.json'
2025-07-15 13:33:55,596 - INFO - SPExtractorAgent initialized.
2025-07-15 13:34:09,706 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:34:09,708 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:34:09,708 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 13:34:46,209 - INFO - SPExtractorAgent initialized.
2025-07-15 13:34:57,509 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:34:57,510 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:34:57,510 - ERROR -  SP Extraction failed: 'utf-8' codec can't decode byte 0x90 in position 22: invalid start byte
2025-07-15 13:36:36,980 - INFO - SPExtractorAgent initialized.
2025-07-15 13:36:50,152 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:36:50,154 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:36:50,154 - INFO - Metadata columns loaded successfully.
2025-07-15 13:36:50,155 - ERROR -  SP Extraction failed: 'list' object has no attribute 'get'
2025-07-15 13:37:34,629 - INFO - SPExtractorAgent initialized.
2025-07-15 13:37:46,109 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:37:46,110 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:37:46,111 - INFO - Metadata columns loaded successfully.
2025-07-15 13:37:46,111 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 13:37:46,111 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 13:37:46,111 - ERROR -  SP Extraction failed: 'function' object has no attribute 'invoke'
2025-07-15 13:38:40,376 - INFO - SPExtractorAgent initialized.
2025-07-15 13:38:53,807 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:38:53,808 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:38:53,809 - INFO - Metadata columns loaded successfully.
2025-07-15 13:38:53,809 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 13:38:53,809 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 13:38:57,624 - INFO - LLM response received.
2025-07-15 13:38:57,624 - INFO - LLM response parsed successfully.
2025-07-15 13:38:57,625 - ERROR -  SP Extraction failed: [Errno 2] No such file or directory: 'outputs/schema_validation.json'
2025-07-15 13:39:42,523 - INFO - SPExtractorAgent initialized.
2025-07-15 13:39:55,725 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:39:55,727 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:39:55,728 - INFO - Metadata columns loaded successfully.
2025-07-15 13:39:55,728 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 13:39:55,728 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 13:39:59,818 - INFO - LLM response received.
2025-07-15 13:39:59,818 - INFO - LLM response parsed successfully.
2025-07-15 13:39:59,819 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 13:39:59,820 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 13:39:59,820 - INFO - SP Extraction completed successfully.
2025-07-15 13:41:38,785 - INFO - SPExtractorAgent initialized.
2025-07-15 13:41:50,766 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:41:50,767 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:41:50,768 - INFO - Metadata columns loaded successfully.
2025-07-15 13:41:50,768 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 13:41:50,768 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 13:41:55,149 - INFO - LLM response received.
2025-07-15 13:41:55,149 - INFO - LLM response parsed successfully.
2025-07-15 13:41:55,150 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 13:41:55,151 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 13:41:55,151 - INFO - SP Extraction completed successfully.
2025-07-15 13:42:43,732 - INFO - SPExtractorAgent initialized.
2025-07-15 13:42:56,797 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:42:56,799 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:42:56,799 - INFO - Metadata columns loaded successfully.
2025-07-15 13:42:56,799 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 13:42:56,800 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 13:43:00,684 - INFO - LLM response received.
2025-07-15 13:43:00,684 - INFO - LLM response parsed successfully.
2025-07-15 13:43:00,685 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 13:43:00,686 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 13:43:00,687 - INFO - SP Extraction completed successfully.
2025-07-15 13:53:52,149 - INFO - SPExtractorAgent initialized.
2025-07-15 13:54:06,617 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:54:06,618 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:54:06,618 - INFO - Metadata columns loaded successfully.
2025-07-15 13:54:06,618 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 13:54:06,618 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 13:54:10,499 - INFO - LLM response received.
2025-07-15 13:54:10,499 - INFO - LLM response parsed successfully.
2025-07-15 13:54:10,500 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 13:54:10,501 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 13:54:10,501 - INFO - SP Extraction completed successfully.
2025-07-15 13:55:56,410 - INFO - SPExtractorAgent initialized.
2025-07-15 13:56:08,610 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 13:56:08,611 - INFO - Stored procedure file loaded successfully.
2025-07-15 13:56:08,611 - INFO - Metadata columns loaded successfully.
2025-07-15 13:56:08,612 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 13:56:08,612 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 13:56:12,677 - INFO - LLM response received.
2025-07-15 13:56:12,677 - INFO - LLM response parsed successfully.
2025-07-15 13:56:12,678 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 13:56:12,679 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 13:56:12,679 - INFO - SP Extraction completed successfully.
2025-07-15 13:58:38,077 - INFO - SPExtractorAgent initialized.
2025-07-15 14:00:15,979 - INFO - SPExtractorAgent initialized.
2025-07-15 14:00:30,115 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:00:30,116 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:00:30,116 - INFO - Metadata columns loaded successfully.
2025-07-15 14:00:30,116 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:00:30,116 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:00:34,472 - INFO - LLM response received.
2025-07-15 14:00:34,472 - INFO - LLM response parsed successfully.
2025-07-15 14:00:34,474 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:00:34,475 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:00:34,475 - INFO - SP Extraction completed successfully.
2025-07-15 14:02:40,625 - INFO - SPExtractorAgent initialized.
2025-07-15 14:02:56,101 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:02:56,102 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:02:56,103 - INFO - Metadata columns loaded successfully.
2025-07-15 14:02:56,103 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:02:56,103 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:03:00,098 - INFO - LLM response received.
2025-07-15 14:03:00,098 - INFO - LLM response parsed successfully.
2025-07-15 14:03:00,099 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:03:00,100 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:03:00,100 - INFO - SP Extraction completed successfully.
2025-07-15 14:09:15,663 - INFO - SPExtractorAgent initialized.
2025-07-15 14:09:27,223 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:09:27,224 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:09:27,224 - INFO - Metadata columns loaded successfully.
2025-07-15 14:09:27,224 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:09:27,225 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:09:30,861 - INFO - LLM response received.
2025-07-15 14:09:30,861 - INFO - LLM response parsed successfully.
2025-07-15 14:09:30,862 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:09:30,863 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:09:30,863 - INFO - SP Extraction completed successfully.
2025-07-15 14:25:47,562 - INFO - SPExtractorAgent initialized.
2025-07-15 14:26:01,970 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:26:01,972 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:26:01,972 - INFO - Metadata columns loaded successfully.
2025-07-15 14:26:01,973 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:26:01,973 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:26:05,389 - INFO - LLM response received.
2025-07-15 14:26:05,389 - INFO - LLM response parsed successfully.
2025-07-15 14:26:05,391 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:26:05,392 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:26:05,392 - INFO - SP Extraction completed successfully.
2025-07-15 14:34:55,142 - INFO - SPExtractorAgent initialized.
2025-07-15 14:35:09,033 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:35:09,034 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:35:09,035 - INFO - Metadata columns loaded successfully.
2025-07-15 14:35:09,035 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:35:09,035 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:35:13,064 - INFO - LLM response received.
2025-07-15 14:35:13,065 - INFO - LLM response parsed successfully.
2025-07-15 14:35:13,066 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:35:13,066 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:35:13,067 - INFO - SP Extraction completed successfully.
2025-07-15 14:36:07,598 - INFO - SPExtractorAgent initialized.
2025-07-15 14:36:20,915 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:36:20,916 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:36:20,916 - INFO - Metadata columns loaded successfully.
2025-07-15 14:36:20,916 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:36:20,916 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:36:24,247 - INFO - LLM response received.
2025-07-15 14:36:24,248 - INFO - LLM response parsed successfully.
2025-07-15 14:36:24,249 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:36:24,250 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:36:24,250 - INFO - SP Extraction completed successfully.
2025-07-15 14:41:46,472 - INFO - SPExtractorAgent initialized.
2025-07-15 14:42:00,534 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:42:00,535 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:42:00,535 - INFO - Metadata columns loaded successfully.
2025-07-15 14:42:00,535 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:42:00,536 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:42:05,122 - INFO - LLM response received.
2025-07-15 14:42:05,122 - INFO - LLM response parsed successfully.
2025-07-15 14:42:05,123 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:42:05,124 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:42:05,124 - INFO - SP Extraction completed successfully.
2025-07-15 14:49:42,846 - INFO - SPExtractorAgent initialized.
2025-07-15 14:49:53,504 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:49:53,506 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:49:53,507 - INFO - Metadata columns loaded successfully.
2025-07-15 14:49:53,507 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:49:53,508 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:49:57,658 - INFO - LLM response received.
2025-07-15 14:49:57,658 - INFO - LLM response parsed successfully.
2025-07-15 14:49:57,659 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:49:57,659 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:49:57,659 - INFO - SP Extraction completed successfully.
2025-07-15 14:51:43,116 - INFO - SPExtractorAgent initialized.
2025-07-15 14:51:55,902 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:51:55,903 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:51:55,904 - INFO - Metadata columns loaded successfully.
2025-07-15 14:51:55,904 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:51:55,904 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:51:59,566 - INFO - LLM response received.
2025-07-15 14:51:59,566 - INFO - LLM response parsed successfully.
2025-07-15 14:51:59,567 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:51:59,568 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:51:59,568 - INFO - SP Extraction completed successfully.
2025-07-15 14:53:47,700 - INFO - SPExtractorAgent initialized.
2025-07-15 14:54:01,795 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:54:01,796 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:54:01,797 - INFO - Metadata columns loaded successfully.
2025-07-15 14:54:01,797 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:54:01,797 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:54:05,922 - INFO - LLM response received.
2025-07-15 14:54:05,923 - INFO - LLM response parsed successfully.
2025-07-15 14:54:05,924 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:54:05,925 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:54:05,925 - INFO - SP Extraction completed successfully.
2025-07-15 14:58:44,398 - INFO - SPExtractorAgent initialized.
2025-07-15 14:58:56,962 - INFO - Running SP extraction for file: data\inputs\store_procedures.json
2025-07-15 14:58:56,966 - INFO - Stored procedure file loaded successfully.
2025-07-15 14:58:56,966 - INFO - Metadata columns loaded successfully.
2025-07-15 14:58:56,966 - INFO - Building prompt for procedure: SP_VALIDATE_FILE_STRUCTURE
2025-07-15 14:58:56,967 - INFO - Prompt built successfully. Sending to LLM...
2025-07-15 14:59:00,766 - INFO - LLM response received.
2025-07-15 14:59:00,766 - INFO - LLM response parsed successfully.
2025-07-15 14:59:00,767 - INFO - Schema validation rules saved at: data/outputs/schema_validation.json
2025-07-15 14:59:00,768 - INFO - Data validation rules saved at: data/outputs/data_validation.json
2025-07-15 14:59:00,768 - INFO - SP Extraction completed successfully.
