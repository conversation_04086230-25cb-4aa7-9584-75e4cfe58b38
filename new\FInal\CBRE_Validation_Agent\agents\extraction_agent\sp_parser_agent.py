import os
import json
from config.llm_config import get_llm
from config.logging import get_logger


class SPExtractorAgent:
    def __init__(self, prompt_path="ContextGuardrail/sp_parser_prompts.txt"):
        self.llm = get_llm()
        self.logger = get_logger("SPExtractorAgent", "logs/sp_parser_agent.log")
        self.prompt_template = self._load_prompt_template(prompt_path)
        self.logger.info("SPExtractorAgent initialized.")

    def _load_prompt_template(self, path):
        with open(path, "r", encoding="utf-8") as f:
            return f.read()

    def _build_prompt(self, procedure_text, procedure_name, metadata_columns):
        required_columns = [col["FILE_COLUMN_NAME"].strip() for col in metadata_columns]
        data_types = {col["FILE_COLUMN_NAME"].strip(): col["Data_Type"] for col in metadata_columns}

        self.logger.info(f"Building prompt for procedure: {procedure_name}")
        return self.prompt_template.format(
            required_columns=json.dumps(required_columns, indent=2),
            data_types=json.dumps(data_types, indent=2),
            procedure_text=procedure_text,
            procedure_name=procedure_name
        )

    def run(self, stored_procedure_file_path, metadata_columns_path):
        try:
            self.logger.info(f"Running SP extraction for file: {stored_procedure_file_path}")

            with open(stored_procedure_file_path, "r", encoding="utf-8-sig") as f:
                sp_data = json.load(f)
            self.logger.info("Stored procedure file loaded successfully.")

            with open(metadata_columns_path, "r", encoding="utf-8-sig") as f:
                metadata_columns = json.load(f)
            self.logger.info("Metadata columns loaded successfully.")

            # Handle both single procedure dict and array of procedures
            if isinstance(sp_data, list):
                # If it's a list, combine all procedures
                procedure_text = ""
                procedure_name = "CombinedProcedures"
                for proc in sp_data:
                    procedure_text += proc.get("procedure_definition", "") + "\n\n"
                    if procedure_name == "CombinedProcedures":
                        procedure_name = proc.get("procedure_name", "UnnamedProcedure")
            else:
                # Single procedure format
                procedure_text = sp_data.get("procedure_definition", "")
                procedure_name = sp_data.get("procedure_name", "UnnamedProcedure")

            prompt = self._build_prompt(procedure_text, procedure_name, metadata_columns)
            self.logger.info("Prompt built successfully. Sending to LLM...")

            response = self.llm(prompt)
            self.logger.info("LLM response received.")

            parsed = json.loads(response)
            self.logger.info("LLM response parsed successfully.")

            schema_path = "data/outputs/schema_validation.json"
            data_json_path = "data/outputs/data_validation.json"

            # Convert the schema validation rules to the expected format
            schema_rules = parsed.get("schema_validation", [])

            # Create a schema definition with columns from the second rule (which has the column list)
            schema_definition = {
                "schema_definition": {
                    "columns": []
                }
            }

            # Find the rule with columns
            for rule in schema_rules:
                if rule.get("columns") and len(rule.get("columns")) > 0:
                    # Create column definitions with appropriate data types
                    for i, col_name in enumerate(rule.get("columns"), 1):
                        # Set data type based on column name
                        data_type = "STRING"
                        if col_name in ["NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL"]:
                            data_type = "NUMBER"

                        schema_definition["schema_definition"]["columns"].append({
                            "column_name": col_name,
                            "data_type": data_type,
                            "is_required": True,
                            "nullable": False,
                            "position": i
                        })
                    break

            with open(schema_path, "w") as f:
                json.dump(schema_definition, f, indent=2)
            self.logger.info(f"Schema validation rules saved at: {schema_path}")

            with open(data_json_path, "w") as f:
                json.dump(parsed.get("data_validation", []), f, indent=2)
            self.logger.info(f"Data validation rules saved at: {data_json_path}")

            self.logger.info("SP Extraction completed successfully.")

            return {
                "success": True,
                "schema_path": schema_path,
                "data_json": data_json_path,
                "message": "SP Extraction successful."
            }

        except Exception as e:
            error_message = f" SP Extraction failed: {e}"
            self.logger.error(error_message)
            return {
                "success": False,
                "message": error_message
            }
