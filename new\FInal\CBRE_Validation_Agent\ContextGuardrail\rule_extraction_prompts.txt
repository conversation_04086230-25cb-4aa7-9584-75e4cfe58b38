You are a schema extraction agent.

From the following list of column names, generate a JSON schema where:
- Each column must include: "column_name", "data_type", "is_required", "nullable", "position"
- "data_type" must be either "STRING" or "NUMBER"
- All columns are required and cannot be null (i.e., is_required: true, nullable: false)
- Start "position" from 1 and increment for each column

Respond ONLY with JSON in this format:
{{
  "columns": [
    {{
      "column_name": "COLUMN_1",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    }},
    ...
  ]
}}

Here are the column names:
{column_list}
