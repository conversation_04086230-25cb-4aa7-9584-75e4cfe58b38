{"timestamp": "20250715_154401", "file_path": "data\\inputs\\FundHoldings_WithErrors.xlsx", "schema_path": "data/outputs/xls_validation_rules.json", "validation_result": {"success": false, "message": "Based on the provided information:\n\n**Actual Columns in File:**\n- \"UNIQUE_ID\"\n- \"PORTFOLIO_ID\"\n- \"REGISTERED_HOLDER\"\n- \"NAV\"\n- \"OWNERSHIP_PERCENTAGE\"\n- \"CAPITAL_CALLED\"\n- \"NO_OF_SHARES\"\n- \"COMMITTED_CAPITAL\"\n- \"PERIOD\"\n- \"FUND_NAME\"\n\n**Expected Schema Columns (with positions):**\n1. \"UNIQUE_ID\" (STRING, required, not nullable)\n2. \"PORTFOLIO_ID\" (STRING, required, not nullable)\n3. \"REGISTERED_HOLDER\" (STRING, required, not nullable)\n4. \"NAV\" (NUMBER, required, not nullable)\n5. \"OWNERSHIP_PERCENTAGE\" (NUMBER, required, not nullable)\n6. \"CAPITAL_CALLED\" (NUMBER, required, not nullable)\n7. \"NO_OF_SHARES\" (NUMBER, required, not nullable)\n8. \"COMMITTED_CAPITAL\" (NUMBER, required, not nullable)\n9. \"PERIOD\" (STRING, required, not nullable)\n10. \"FUND_NAME\" (STRING, required, not nullable)\n\n**Comparison and Validation:**\n\n- **Columns Present:** All expected columns are present in the file.\n- **Column Order:** Matches the expected position.\n- **Data Types:**\n  - \"UNIQUE_ID\": String, sample \"UID_8637\" → valid.\n  - \"PORTFOLIO_ID\": String, sample \"PORT_982\" → valid.\n  - \"REGISTERED_HOLDER\": String, sample \"Holder_1\" → valid.\n  - \"NAV\": Number, sample 420278.83 → valid.\n  - \"OWNERSHIP_PERCENTAGE\": Number, sample 57.98 → valid.\n  - \"CAPITAL_CALLED\": Number, sample 36688.6 → valid.\n  - \"NO_OF_SHARES\": Number, sample 2891.0 → valid.\n  - \"COMMITTED_CAPITAL\": Number, sample 444628.33 → valid.\n  - \"PERIOD\": String, sample \"Q3-2025\" → valid.\n  - \"FUND_NAME\": String, sample \"Beta Fund\" → valid.\n\n- **Nullability & Required Fields:**\n  - \"OWNERSHIP_PERCENTAGE\" has a null value (\"null\") in one record (UID_2072). Since it's required and non-nullable, this is a violation.\n\n**Summary:**\n- All columns are present and correctly ordered.\n- Data types are consistent with expectations.\n- All required fields are present.\n- **Issue:** One record has a null value for a non-nullable required field (\"OWNERSHIP_PERCENTAGE\").\n\n**Final Verdict:**\n\n**INVALID**\n\nThe file contains a null value in a required, non-nullable field (\"OWNERSHIP_PERCENTAGE\") in at least one record."}, "actual_columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "sample_data": [{"UNIQUE_ID": "UID_8637", "PORTFOLIO_ID": "PORT_982", "REGISTERED_HOLDER": "Holder_1", "NAV": 420278.83, "OWNERSHIP_PERCENTAGE": 57.98, "CAPITAL_CALLED": 36688.6, "NO_OF_SHARES": 2891.0, "COMMITTED_CAPITAL": 444628.33, "PERIOD": "Q3-2025", "FUND_NAME": "Beta Fund"}, {"UNIQUE_ID": "UID_9449", "PORTFOLIO_ID": "PORT_739", "REGISTERED_HOLDER": "Holder_64", "NAV": 408952.82, "OWNERSHIP_PERCENTAGE": 74.61, "CAPITAL_CALLED": 168681.11, "NO_OF_SHARES": 639.0, "COMMITTED_CAPITAL": 168343.59, "PERIOD": "Q1-2025", "FUND_NAME": "Delta Fund"}, {"UNIQUE_ID": "UID_2072", "PORTFOLIO_ID": "PORT_533", "REGISTERED_HOLDER": "Holder_44", "NAV": 96863.47, "OWNERSHIP_PERCENTAGE": null, "CAPITAL_CALLED": 297529.46, "NO_OF_SHARES": 7199.0, "COMMITTED_CAPITAL": 495606.56, "PERIOD": "Q3-2025", "FUND_NAME": "Beta Fund"}, {"UNIQUE_ID": "UID_8735", "PORTFOLIO_ID": "PORT_293", "REGISTERED_HOLDER": "Holder_20", "NAV": 191019.27, "OWNERSHIP_PERCENTAGE": 5.55, "CAPITAL_CALLED": 167299.91, "NO_OF_SHARES": 6076.0, "COMMITTED_CAPITAL": 470566.79, "PERIOD": "Q2-2025", "FUND_NAME": "Delta Fund"}, {"UNIQUE_ID": "UID_4088", "PORTFOLIO_ID": "PORT_999", "REGISTERED_HOLDER": "Holder_9", "NAV": 64727.77, "OWNERSHIP_PERCENTAGE": 92.5, "CAPITAL_CALLED": 108966.1, "NO_OF_SHARES": 6203.0, "COMMITTED_CAPITAL": 236765.15, "PERIOD": "Q2-2025", "FUND_NAME": "Gamma Fund"}]}