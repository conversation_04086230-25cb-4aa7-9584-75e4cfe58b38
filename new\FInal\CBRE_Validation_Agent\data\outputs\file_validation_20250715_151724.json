{"timestamp": "20250715_151724", "file_path": "data\\inputs\\Fundholding_Data _2 1.xlsx", "schema_path": "data/outputs/schema_validation.json", "validation_result": {"success": true, "message": "Based on the provided information:\n\n**Actual Columns in File:**\n- \"UNIQUE_ID\"\n- \"PORTFOLIO_ID\"\n- \"REGISTERED_HOLDER\"\n- \"NAV\"\n- \"OWNERSHIP_PERCENTAGE\"\n- \"CAPITAL_CALLED\"\n- \"NO_OF_SHARES\"\n- \"COMMITTED_CAPITAL\"\n- \"PERIOD\"\n- \"FUND_NAME\"\n\n**Expected Columns (with positions):**\n1. \"UNIQUE_ID\" (STRING, required, not nullable)\n2. \"PORTFOLIO_ID\" (STRING, required, not nullable)\n3. \"REGISTERED_HOLDER\" (STRING, required, not nullable)\n4. \"NAV\" (NUMBER, required, not nullable)\n5. \"OWNERSHIP_PERCENTAGE\" (NUMBER, required, not nullable)\n6. \"CAPITAL_CALLED\" (NUMBER, required, not nullable)\n7. \"NO_OF_SHARES\" (NUMBER, required, not nullable)\n8. \"COMMITTED_CAPITAL\" (NUMBER, required, not nullable)\n9. \"PERIOD\" (STRING, required, not nullable)\n10. \"FUND_NAME\" (STRING, required, not nullable)\n\n**Validation:**\n\n- **Columns match exactly** with expected columns, no missing or extra columns.\n- **Order of columns** in the file matches the expected 'position'.\n- **Data types** in sample data align with expected types:\n  - String fields (\"UNIQUE_ID\", \"PORTFOLIO_ID\", \"REGISTERED_HOLDER\", \"PERIOD\", \"FUND_NAME\") contain string values.\n  - Numeric fields (\"NAV\", \"OWNERSHIP_PERCENTAGE\", \"CAPITAL_CALLED\", \"NO_OF_SHARES\", \"COMMITTED_CAPITAL\") contain numeric values.\n- **Required and non-null constraints** are satisfied:\n  - All required columns are present.\n  - Sample data shows no nulls in required fields.\n\n**Conclusion:**\n\nThe uploaded file's structure, columns, order, and data types conform to the predefined schema, and all required fields are present with valid data.\n\n**Final verdict:**\n\n**VALID**"}, "actual_columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "sample_data": [{"UNIQUE_ID": "UID_8270", "PORTFOLIO_ID": "PORT_646", "REGISTERED_HOLDER": "Holder_56", "NAV": 361352.84, "OWNERSHIP_PERCENTAGE": 4, "CAPITAL_CALLED": 94555.55, "NO_OF_SHARES": 4264, "COMMITTED_CAPITAL": 191240.14, "PERIOD": "Q3-2023", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_1860", "PORTFOLIO_ID": "PORT_838", "REGISTERED_HOLDER": "Holder_81", "NAV": 343382.57, "OWNERSHIP_PERCENTAGE": 5, "CAPITAL_CALLED": 124852.64, "NO_OF_SHARES": 8041, "COMMITTED_CAPITAL": 175229.36, "PERIOD": "Q2-2024", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_6390", "PORTFOLIO_ID": "PORT_712", "REGISTERED_HOLDER": "Holder_59", "NAV": 150921.19, "OWNERSHIP_PERCENTAGE": 3, "CAPITAL_CALLED": 398374.24, "NO_OF_SHARES": 5669, "COMMITTED_CAPITAL": 459640.88, "PERIOD": "Q3-2025", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_6191", "PORTFOLIO_ID": "PORT_561", "REGISTERED_HOLDER": "Holder_2", "NAV": 370480.65, "OWNERSHIP_PERCENTAGE": 7, "CAPITAL_CALLED": 281800.77, "NO_OF_SHARES": 4400, "COMMITTED_CAPITAL": 350068.61, "PERIOD": "Q1-2024", "FUND_NAME": "Alpha Fund"}, {"UNIQUE_ID": "UID_6734", "PORTFOLIO_ID": "PORT_742", "REGISTERED_HOLDER": "Holder_2", "NAV": 156762.09, "OWNERSHIP_PERCENTAGE": 5, "CAPITAL_CALLED": 159838.71, "NO_OF_SHARES": 6369, "COMMITTED_CAPITAL": 234229.2, "PERIOD": "Q2-2024", "FUND_NAME": "Alpha Fund"}]}