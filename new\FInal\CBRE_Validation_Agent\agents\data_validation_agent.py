import os
import json
import yaml
import pandas as pd
from dotenv import load_dotenv
from openai import OpenAI
from collections import Counter
from typing import List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import re

# Load environment
load_dotenv()

class DataValidationAgent:
    def __init__(
        self,
        model_config_path: str = "config/model_config.yaml",
        log=None,
        max_workers: int = 4
    ):
        self.model_config_path = model_config_path
        self.logger = log
        self.max_workers = max_workers

        self._load_model_config()
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        self.system_prompt_template = ""
        self.validation_rules = []

    def _load_model_config(self):
        with open(self.model_config_path, "r") as f:
            model_config = yaml.safe_load(f)
        self.model_name = model_config["openai"]["model"]
        self.temperature = model_config["openai"].get("temperature", 0.2)

    def load_prompt(self, prompt_path: str):
        with open(prompt_path, "r", encoding="utf-8") as file:
            self.system_prompt_template = file.read()

    def load_rules(self, rules_path: str):
        with open(rules_path, "r", encoding="utf-8") as file:
            self.validation_rules = json.load(file)

    def normalize_column(self, col_name: str):
        return re.sub(r"[ _]", "", col_name.lower())

    def find_column(self, df: pd.DataFrame, target_column: str):
        normalized_target = self.normalize_column(target_column)
        for col in df.columns:
            if self.normalize_column(col) == normalized_target:
                return col
        return None

    def ownership_check(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        results = []
        ownership_col = self.find_column(df, "OWNERSHIP_PERCENTAGE")
        if ownership_col:
            # Convert to numeric and handle errors
            df[ownership_col] = pd.to_numeric(df[ownership_col], errors="coerce").fillna(0)
            ownership_sum = df[ownership_col].sum()

            # Check if sum equals exactly 100
            if ownership_sum == 100.0:
                results.append({
                    "is_correct": True,
                    "why": f"Sum of ownership_percentage is exactly 100.0 (actual: {ownership_sum}). Validation can proceed."
                })
            else:
                results.append({
                    "is_correct": False,
                    "why": f"Sum of ownership_percentage must be exactly 100.0, but found {ownership_sum}. File rejected - validation cannot proceed."
                })
        else:
            results.append({
                "is_correct": False,
                "why": "Column ownership_percentage not found. File rejected - validation cannot proceed."
            })
        return results

    def build_prompt(self, row: Dict[str, Any], row_index: int) -> str:
        return self.system_prompt_template.format(
            validation_rules=json.dumps(self.validation_rules, indent=2),
            row_data=json.dumps(row, indent=2),
            row_index=row_index
        )

    def validate_row(self, prompt: str) -> Dict[str, Any]:
        response = self.client.chat.completions.create(
            model=self.model_name,
            messages=[
                {"role": "system", "content": "You are a data validation expert."},
                {"role": "user", "content": prompt}
            ],
            temperature=self.temperature
        )
        content = response.choices[0].message.content.strip()

        # Try to extract JSON if it's wrapped in other text
        json_pattern = r'({.*?})'
        json_matches = re.findall(json_pattern, content, re.DOTALL)

        if json_matches:
            # Try each potential JSON match
            for json_str in json_matches:
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    continue

        # If no valid JSON found in matches, try the original content
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            # If all else fails, create a valid response
            return {"is_correct": True, "why": "All validations passed"}

    def _validate_single_row(self, row_data: Dict[str, Any], row_index: int) -> Tuple[int, Dict[str, Any]]:
        prompt = self.build_prompt(row_data, row_index)
        result = self.validate_row(prompt)
        return row_index, result

    def run(
        self,
        excel_file_path: str,
        output_json_path: str = "data/outputs/validation_results.json",
        output_excel_path: str = "data/outputs/validation_results.xlsx"
    ) -> Dict[str, Any]:

        if not os.path.exists(excel_file_path):
            raise FileNotFoundError(f"Excel file not found: {excel_file_path}")

        # Count total rows including header for reporting
        total_rows = len(pd.read_excel(excel_file_path, header=None))

        # Normal processing with header
        df = pd.read_excel(excel_file_path)
        os.makedirs("data/outputs", exist_ok=True)

        # Ownership checks
        ownership_results = self.ownership_check(df)
        with open("data/outputs/initial_check_results.json", "w", encoding="utf-8") as f:
            json.dump(ownership_results, f, indent=2)

        if any(not res["is_correct"] for res in ownership_results):
            print("🚨 Ownership checks failed. See initial_check_results.json.")
            return {"success": False, "ownership_results": ownership_results}

        # Duplicate check
        unique_id_counts = Counter(df["UNIQUE_ID"].astype(str))
        tasks = []
        for idx, row in df.iterrows():
            row_data = row.to_dict()
            unique_id = str(row_data.get("UNIQUE_ID", ""))
            row_data["duplicate_in_dataset"] = unique_id_counts[unique_id] > 1
            tasks.append((row_data, idx))

        validation_results = [None] * len(df)

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_index = {
                executor.submit(self._validate_single_row, row_data, idx): idx
                for row_data, idx in tasks
            }
            for future in as_completed(future_to_index):
                row_index, result = future.result()
                validation_results[row_index] = {
                    **tasks[row_index][0],  # original row data
                    "is_correct": result.get("is_correct", False),
                    "reason": result.get("why", "No reason provided")
                }
                status = "✅" if result.get("is_correct", False) else "❌"
                print(f"{status} Row {row_index + 1}: {result.get('why')}")

        # Save outputs
        with open(output_json_path, "w", encoding="utf-8") as f:
            json.dump(validation_results, f, indent=2)
        df_out = pd.DataFrame(validation_results)
        df_out.to_excel(output_excel_path, index=False)

        print(f"\n📁 JSON saved to: {output_json_path}")
        print(f"📁 Excel saved to: {output_excel_path}")
        print(f"📊 Total rows processed: {total_rows} (including header)")
        return {
            "success": True,
            "ownership_results": ownership_results,
            "validated_rows": len(validation_results),
            "total_rows": total_rows
        }