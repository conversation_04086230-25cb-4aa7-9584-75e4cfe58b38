# Placeholder for ContextGuardrail/file_validation_prompts.txt

You are a file validation agent.

You will validate the uploaded Excel file based on:
1. Predefined schema (columns, data types, required fields, nullable)
2. Actual columns in the uploaded file
3. Sample preview of the data

Expected Schema:
{
  "columns": [
    {
      "column_name": "UNIQUE_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    {
      "column_name": "PORTFOLIO_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 2
    },
    {
      "column_name": "REGISTERED_HOLDER",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 3
    },
    {
      "column_name": "NAV",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 4
    },
    {
      "column_name": "OWNERSHIP_PERCENTAGE",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 5
    },
    {
      "column_name": "CAPITAL_CALLED",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 6
    },
    {
      "column_name": "NO_OF_SHARES",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 7
    },
    {
      "column_name": "COMMITTED_CAPITAL",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 8
    },
    {
      "column_name": "PERIOD",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 9
    },
    {
      "column_name": "FUND_NAME",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 10
    }
  ]
}

Actual Columns in File:
[
  "UNIQUE_ID",
  "PORTFOLIO_ID",
  "REGISTERED_HOLDER",
  "NAV",
  "OWNERSHIP_PERCENTAGE",
  "CAPITAL_CALLED",
  "NO_OF_SHARES",
  "COMMITTED_CAPITAL",
  "PERIOD",
  "FUND_NAME"
]

Sample Data:
[
  {
    "UNIQUE_ID": "UID_8270",
    "PORTFOLIO_ID": "PORT_646",
    "REGISTERED_HOLDER": "Holder_56",
    "NAV": 361352.84,
    "OWNERSHIP_PERCENTAGE": 4,
    "CAPITAL_CALLED": 94555.55,
    "NO_OF_SHARES": 4264,
    "COMMITTED_CAPITAL": 191240.14,
    "PERIOD": "Q3-2023",
    "FUND_NAME": "Alpha Fund"
  },
  {
    "UNIQUE_ID": "UID_1860",
    "PORTFOLIO_ID": "PORT_838",
    "REGISTERED_HOLDER": "Holder_81",
    "NAV": 343382.57,
    "OWNERSHIP_PERCENTAGE": 5,
    "CAPITAL_CALLED": 124852.64,
    "NO_OF_SHARES": 8041,
    "COMMITTED_CAPITAL": 175229.36,
    "PERIOD": "Q2-2024",
    "FUND_NAME": "Alpha Fund"
  },
  {
    "UNIQUE_ID": "UID_6390",
    "PORTFOLIO_ID": "PORT_712",
    "REGISTERED_HOLDER": "Holder_59",
    "NAV": 150921.19,
    "OWNERSHIP_PERCENTAGE": 3,
    "CAPITAL_CALLED": 398374.24,
    "NO_OF_SHARES": 5669,
    "COMMITTED_CAPITAL": 459640.88,
    "PERIOD": "Q3-2025",
    "FUND_NAME": "Alpha Fund"
  },
  {
    "UNIQUE_ID": "UID_6191",
    "PORTFOLIO_ID": "PORT_561",
    "REGISTERED_HOLDER": "Holder_2",
    "NAV": 370480.65,
    "OWNERSHIP_PERCENTAGE": 7,
    "CAPITAL_CALLED": 281800.77,
    "NO_OF_SHARES": 4400,
    "COMMITTED_CAPITAL": 350068.61,
    "PERIOD": "Q1-2024",
    "FUND_NAME": "Alpha Fund"
  },
  {
    "UNIQUE_ID": "UID_6734",
    "PORTFOLIO_ID": "PORT_742",
    "REGISTERED_HOLDER": "Holder_2",
    "NAV": 156762.09,
    "OWNERSHIP_PERCENTAGE": 5,
    "CAPITAL_CALLED": 159838.71,
    "NO_OF_SHARES": 6369,
    "COMMITTED_CAPITAL": 234229.2,
    "PERIOD": "Q2-2024",
    "FUND_NAME": "Alpha Fund"
  }
]

Instructions:
- Highlight missing or extra columns.
- Ensure column order matches expected 'position'.
- Validate types (STRING vs NUMBER).
- Strictly enforce non-null and required fields.
- Do not assume anything beyond this input.

End with a clear verdict:
- VALID (if file structure is acceptable)
- INVALID (if any issues found)
