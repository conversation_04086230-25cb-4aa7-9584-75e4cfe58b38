import os
import json
from config.llm_config import get_llm
from config.logging import get_logger
 
def log_to_jsonl(filepath, record):
    with open(filepath, "a", encoding="utf-8") as f:
        f.write(json.dumps(record) + "\n")

def log_human_readable(filepath, message):
    with open(filepath, "a", encoding="utf-8") as f:
        f.write(message + "\n")
 
class GuardRailAgent:
    def __init__(self):
        self.llm = get_llm()
        self.logger = get_logger("GuardRailAgent", "logs/guardrail_agent.log")
        # Ensure correct path to your ContextGuardrail prompts folder
        self.prompt_dir = os.path.abspath(os.path.join(
            os.path.dirname(__file__), "../ContextGuardrail"))
 
    def _load_prompt(self, filename: str) -> str:
        prompt_path = os.path.join(self.prompt_dir, filename)
        if not os.path.exists(prompt_path):
            raise FileNotFoundError(f"Prompt file not found: {prompt_path}")
        with open(prompt_path, "r", encoding="utf-8") as f:
            return f.read()
 
    def check(self, failure_reason: str) -> dict:
        prompt = f"""
You are a strict validation expert. Your job is to check if the following failure reason indicates a valid failure or is caused by hallucination, irrelevant errors, or sensitive data leak.
 
Failure Reason:
"{failure_reason}"
 
Respond only with:
VALID - if the failure reason is genuine and process can stop.
INVALID - if the reason looks suspicious, hallucinated, or irrelevant and process should not stop.
 
Now analyze and answer.
"""
        response = self.llm(prompt)
        if "INVALID" in response.upper():
            return {"valid": False, "message": "Guardrail blocked due to invalid failure reason."}
        return {"valid": True, "message": "Failure reason is valid."}
 
    def check_hallucinated_schema(self, error_message: str, known_schema: list[str]) -> dict:
        prompt = self._load_prompt("check_hallucinated_schema.txt")
        filled_prompt = prompt.replace("{error_message}", error_message).replace(
            "{known_schema}", ", ".join(known_schema)
        )
        response = self.llm(filled_prompt)
        if "HALLUCINATED" in response.upper():
            return {
                "valid": False,
                "message": f"Hallucinated columns suspected: {response.strip()}"
            }
        return {"valid": True, "message": "No hallucinated schema elements found."}
 
    def check_invalid_rule_applications(self, validation_output: dict, business_rules: list[dict]) -> dict:
        prompt = self._load_prompt("check_invalid_rule_applications.txt")
        filled_prompt = prompt.replace("{validation_output}", str(validation_output)).replace(
            "{business_rules}", str(business_rules)
        )
        response = self.llm(filled_prompt)
        if "INVALID_RULE" in response.upper():
            return {
                "valid": False,
                "message": f"Invalid rule application detected: {response.strip()}"
            }
        return {"valid": True, "message": "All applied rules align with business rules."}
 
    def check_weak_justifications(self, validation_output: dict) -> dict:
        prompt = self._load_prompt("check_weak_justifications.txt")
        filled_prompt = prompt.replace(
            "{validation_output}", str(validation_output))
        response = self.llm(filled_prompt)
        if "WEAK_JUSTIFICATION" in response.upper():
            return {
                "valid": False,
                "message": f"Weak override justification suspected: {response.strip()}"
            }
        return {"valid": True, "message": "Justifications are strong or not required."}
 
    def run(self, input_files: dict, config: dict) -> dict:
        check_type = config.get("check_type")
 
        try:
            if check_type == "basic":
                failure_reason = input_files.get("failure_reason", "")
                result = self.check(failure_reason)
 
            elif check_type == "schema":
                error_msg = input_files.get("error_message", "")
                schema = input_files.get("known_schema", [])
                result = self.check_hallucinated_schema(error_msg, schema)
 
            elif check_type == "rules":
                validation_output = input_files.get("validation_output", {})
                business_rules = input_files.get("business_rules", [])
                result = self.check_invalid_rule_applications(
                    validation_output, business_rules)
 
            elif check_type == "justification":
                validation_output = input_files.get("validation_output", {})
                result = self.check_weak_justifications(validation_output)
 
            else:
                return {
                    "status": "FAILED",
                    "message": f"Unknown check_type '{check_type}'",
                    "output_file": None
                }
 
            # Log the result
            audit_record = {
                "check_type": check_type,
                "status": "PASSED" if result["valid"] else "FAILED",
                "message": result["message"]
            }
 
            log_to_jsonl("logs/audit_logs.jsonl", audit_record)
            log_human_readable("logs/audit_human_readable.log",
                               f"[{audit_record['status']}] {check_type}: {audit_record['message']}")
 
            return {
                "status": audit_record["status"],
                "message": audit_record["message"],
                "output_file": None
            }
 
        except Exception as e:
            err_msg = f"Guardrail execution failed: {str(e)}"
            log_to_jsonl("logs/audit_logs.jsonl",
                         {"check_type": check_type, "status": "ERROR", "message": err_msg})
            log_human_readable("logs/audit_human_readable.log",
                               f"[ERROR] {check_type}: {err_msg}")
            return {
                "status": "ERROR",
                "message": err_msg,
                "output_file": None
            }