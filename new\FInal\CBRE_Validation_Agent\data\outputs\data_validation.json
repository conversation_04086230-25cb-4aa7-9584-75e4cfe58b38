[{"rule": "Unique identifiers should not be null or empty", "source_procedure": "SP_FINAL_VALIDATION", "columns": ["UNIQUE_ID"], "type": "null_check"}, {"rule": "Unique identifiers should be unique", "source_procedure": "SP_FINAL_VALIDATION", "columns": ["UNIQUE_ID"], "type": "duplicate_check"}, {"rule": "NAV, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL should not be zero or null", "source_procedure": "SP_CLEAN_DATA", "columns": ["NAV", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL"], "type": "null_check / value_range"}, {"rule": "OWNERSHIP_PERCENTAGE should not be null (range validation is done at file level)", "source_procedure": "SP_CLEAN_DATA", "columns": ["OWNERSHIP_PERCENTAGE"], "type": "null_check"}]