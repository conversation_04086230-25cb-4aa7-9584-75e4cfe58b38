{"timestamp": "20250715_153539", "file_path": "data\\inputs\\FundHoldings_WithErrors.xlsx", "schema_path": "data/outputs/xls_validation_rules.json", "validation_result": {"success": false, "message": "Based on the provided actual columns, sample data, and the expected schema, here is the validation analysis:\n\n1. Columns in File vs Expected Schema:\n   - Actual columns exactly match the expected columns in name and order.\n   - No missing columns.\n   - No extra columns.\n\n2. Column Order:\n   - The order of columns in the file matches the 'position' specified in the schema.\n\n3. Data Types:\n   - Sample data for each column aligns with the expected data types:\n     - STRING: \"UNIQUE_ID\", \"PORTFOLIO_ID\", \"REGISTERED_HOLDER\", \"PERIOD\", \"FUND_NAME\" all contain string values.\n     - NUMBER: \"NAV\", \"OWNERSHIP_PERCENTAGE\", \"CAPITAL_CALLED\", \"NO_OF_SHARES\", \"COMMITTED_CAPITAL\" contain numeric values.\n   - Notably, \"OWNERSHIP_PERCENTAGE\" has a null value in one record, but since it is marked as 'nullable: false', this is a violation.\n\n4. Required and Non-null Fields:\n   - All columns are marked as required and non-nullable.\n   - The sample data shows \"OWNERSHIP_PERCENTAGE\" as null in one record, which violates the non-null constraint.\n\n**Summary of issues:**\n- The column structure (names, order) is correct.\n- The data types are consistent with expectations.\n- The main issue is with the \"OWNERSHIP_PERCENTAGE\" field being null in at least one record, which violates the non-nullable requirement.\n\n**Final verdict:**\n**INVALID**\n\nThe file contains a null value in a non-nullable required field (\"OWNERSHIP_PERCENTAGE\")."}, "actual_columns": ["UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"], "sample_data": [{"UNIQUE_ID": "UID_8637", "PORTFOLIO_ID": "PORT_982", "REGISTERED_HOLDER": "Holder_1", "NAV": 420278.83, "OWNERSHIP_PERCENTAGE": 57.98, "CAPITAL_CALLED": 36688.6, "NO_OF_SHARES": 2891.0, "COMMITTED_CAPITAL": 444628.33, "PERIOD": "Q3-2025", "FUND_NAME": "Beta Fund"}, {"UNIQUE_ID": "UID_9449", "PORTFOLIO_ID": "PORT_739", "REGISTERED_HOLDER": "Holder_64", "NAV": 408952.82, "OWNERSHIP_PERCENTAGE": 74.61, "CAPITAL_CALLED": 168681.11, "NO_OF_SHARES": 639.0, "COMMITTED_CAPITAL": 168343.59, "PERIOD": "Q1-2025", "FUND_NAME": "Delta Fund"}, {"UNIQUE_ID": "UID_2072", "PORTFOLIO_ID": "PORT_533", "REGISTERED_HOLDER": "Holder_44", "NAV": 96863.47, "OWNERSHIP_PERCENTAGE": null, "CAPITAL_CALLED": 297529.46, "NO_OF_SHARES": 7199.0, "COMMITTED_CAPITAL": 495606.56, "PERIOD": "Q3-2025", "FUND_NAME": "Beta Fund"}, {"UNIQUE_ID": "UID_8735", "PORTFOLIO_ID": "PORT_293", "REGISTERED_HOLDER": "Holder_20", "NAV": 191019.27, "OWNERSHIP_PERCENTAGE": 5.55, "CAPITAL_CALLED": 167299.91, "NO_OF_SHARES": 6076.0, "COMMITTED_CAPITAL": 470566.79, "PERIOD": "Q2-2025", "FUND_NAME": "Delta Fund"}, {"UNIQUE_ID": "UID_4088", "PORTFOLIO_ID": "PORT_999", "REGISTERED_HOLDER": "Holder_9", "NAV": 64727.77, "OWNERSHIP_PERCENTAGE": 92.5, "CAPITAL_CALLED": 108966.1, "NO_OF_SHARES": 6203.0, "COMMITTED_CAPITAL": 236765.15, "PERIOD": "Q2-2025", "FUND_NAME": "Gamma Fund"}]}