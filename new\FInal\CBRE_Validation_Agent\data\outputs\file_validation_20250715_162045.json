{"timestamp": "20250715_162045", "file_path": "data\\inputs\\Fundholding_Data _2 1.xlsx", "schema_path": "data/outputs/xls_validation_rules.json", "validation_result": {"success": false, "message": "The uploaded file contains the following observations:\n\n1. **Columns Present in File:**\n   - UNIQUE_ID\n   - REGISTERED_HOLDER\n   - NAV\n   - OWNERSHIP_PERCENTAGE\n   - CAPITAL_CALLED\n   - NO_OF_SHARES\n   - COMMITTED_CAPITAL\n   - PERIOD\n   - FUND_NAME\n   - PORTFOLIO_ID\n\n2. **Comparison with Expected Schema:**\n\n| Expected Column             | Actual Column in File | Status                     | Notes                                              |\n|------------------------------|------------------------|----------------------------|----------------------------------------------------|\n| UNIQUE_ID                    | Present                | OK                         |                                                    |\n| PORTFOLIO_ID                 | Present                | OK                         |                                                    |\n| REGISTERED_HOLDER            | Present                | OK                         |                                                    |\n| NAV                          | Present                | OK                         |                                                    |\n| OWNERSHIP_PERCENTAGE         | Present                | OK                         |                                                    |\n| CAPITAL_CALLED               | Present                | OK                         |                                                    |\n| NO_OF_SHARES                 | Present                | OK                         |                                                    |\n| COMMITTED_CAPITAL            | Present                | OK                         |                                                    |\n| PERIOD                       | Present                | OK                         |                                                    |\n| FUND_NAME                    | Present                | OK                         |                                                    |\n\n3. **Column Order:**\n   - The order in the file is:\n     1. UNIQUE_ID\n     2. REGISTERED_HOLDER\n     3. NAV\n     4. OWNERSHIP_PERCENTAGE\n     5. CAPITAL_CALLED\n     6. NO_OF_SHARES\n     7. COMMITTED_CAPITAL\n     8. PERIOD\n     9. FUND_NAME\n     10. PORTFOLIO_ID\n\n   - Expected order based on 'position':\n     1. UNIQUE_ID\n     2. PORTFOLIO_ID\n     3. REGISTERED_HOLDER\n     4. NAV\n     5. OWNERSHIP_PERCENTAGE\n     6. CAPITAL_CALLED\n     7. NO_OF_SHARES\n     8. COMMITTED_CAPITAL\n     9. PERIOD\n     10. FUND_NAME\n\n   - **Issue:** The actual columns are not in the expected order. Specifically, 'PORTFOLIO_ID' appears last, whereas it should be second.\n\n4. **Data Types & Nullability:**\n   - Sample data indicates:\n     - 'UNIQUE_ID', 'PORTFOLIO_ID', 'REGISTERED_HOLDER', 'PERIOD', 'FUND_NAME' are strings.\n     - 'NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL' are numbers.\n   - All required fields are present and non-null in sample data.\n\n5. **Missing or Extra Columns:**\n   - No columns are missing.\n   - No extra columns beyond the expected set.\n\n**Summary:**\n- The file contains all required columns with correct data types and non-null values.\n- The only issue is the order of columns: it does not match the expected 'position' order.\n\n**Final Verdict:**\n**INVALID** — because the column order does not match the specified schema."}, "actual_columns": ["UNIQUE_ID", "REGISTERED_HOLDER", "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME", "PORTFOLIO_ID"], "sample_data": [{"UNIQUE_ID": "UID_8270", "REGISTERED_HOLDER": "Holder_56", "NAV": 361352.84, "OWNERSHIP_PERCENTAGE": 4, "CAPITAL_CALLED": 94555.55, "NO_OF_SHARES": 4264, "COMMITTED_CAPITAL": 191240.14, "PERIOD": "Q3-2023", "FUND_NAME": "Alpha Fund", "PORTFOLIO_ID": "PORT_646"}, {"UNIQUE_ID": "UID_1860", "REGISTERED_HOLDER": "Holder_81", "NAV": 343382.57, "OWNERSHIP_PERCENTAGE": 5, "CAPITAL_CALLED": 124852.64, "NO_OF_SHARES": 8041, "COMMITTED_CAPITAL": 175229.36, "PERIOD": "Q2-2024", "FUND_NAME": "Alpha Fund", "PORTFOLIO_ID": "PORT_838"}, {"UNIQUE_ID": "UID_6390", "REGISTERED_HOLDER": "Holder_59", "NAV": 150921.19, "OWNERSHIP_PERCENTAGE": 3, "CAPITAL_CALLED": 398374.24, "NO_OF_SHARES": 5669, "COMMITTED_CAPITAL": 459640.88, "PERIOD": "Q3-2025", "FUND_NAME": "Alpha Fund", "PORTFOLIO_ID": "PORT_712"}, {"UNIQUE_ID": "UID_6191", "REGISTERED_HOLDER": "Holder_2", "NAV": 370480.65, "OWNERSHIP_PERCENTAGE": 7, "CAPITAL_CALLED": 281800.77, "NO_OF_SHARES": 4400, "COMMITTED_CAPITAL": 350068.61, "PERIOD": "Q1-2024", "FUND_NAME": "Alpha Fund", "PORTFOLIO_ID": "PORT_561"}, {"UNIQUE_ID": "UID_6734", "REGISTERED_HOLDER": "Holder_2", "NAV": 156762.09, "OWNERSHIP_PERCENTAGE": 5, "CAPITAL_CALLED": 159838.71, "NO_OF_SHARES": 6369, "COMMITTED_CAPITAL": 234229.2, "PERIOD": "Q2-2024", "FUND_NAME": "Alpha Fund", "PORTFOLIO_ID": "PORT_742"}]}