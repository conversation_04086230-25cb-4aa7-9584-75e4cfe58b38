from agents.extraction_agent.rule_base_agent import RuleExtractionAgent
from agents.file_validation_agent import FileValidationAgent
from agents.extraction_agent.sp_parser_agent import SPExtractorAgent
from agents.data_validation_agent import DataValidationAgent
from agents.guardrail import GuardRailAgent
from config.llm_config import get_llm
from config.logging import get_logger


class MasterValidationAgent:
    def __init__(self):
        self.llm = get_llm()
        self.logger = get_logger("MasterValidationAgent", "logs/master_agent.log")

        self.rule_agent = RuleExtractionAgent()
        self.file_agent = FileValidationAgent()
        self.sp_agent = SPExtractorAgent()
        self.data_validator = DataValidationAgent()
        self.guardrail_agent = GuardRailAgent()

    def log(self, message: str):
        self.logger.info(message)

    def guardrail_check(self, result: dict) -> bool:
        guard_result = self.guardrail_agent.check(result.get("message", ""))
        if not guard_result.get("valid", True):
            self.log(f"[GUARDRAIL] Blocked: {guard_result['message']}")
            return False
        return True

    def run(self, metadata_excel_path: str, user_excel_path: str, stored_procedure_file_path: str) -> dict:
        self.log("[MASTER] Starting Multi-Agent Orchestration")

        # Step 1: Rule Extraction
        rule_result = self.rule_agent.run(metadata_excel_path)
        self.log(f"[RULE AGENT] {rule_result}")
        if not rule_result.get("success"):
            if not self.guardrail_check(rule_result):
                return {"success": False, "message": "GuardRail blocked after Rule Extraction."}
            return {"success": False, "message": "Failed at Rule Extraction."}

        # Step 2: File Validation (Metadata Schema)
        schema_path = rule_result.get("schema_path")
        file_result = self.file_agent.run(user_excel_path, schema_path)
        self.log(f"[FILE VALIDATION AGENT] {file_result}")
        if not file_result.get("success"):
            if not self.guardrail_check(file_result):
                return {"success": False, "message": "GuardRail blocked after File Validation."}
            return {"success": False, "message": "Failed at File Validation."}

        # Step 3: Stored Procedure Parsing (SP + Metadata Columns)
        # Convert Excel path to JSON path for SP parser
        metadata_json_path = metadata_excel_path.replace(".xlsx", ".json")
        sp_result = self.sp_agent.run(
            stored_procedure_file_path=stored_procedure_file_path,
            metadata_columns_path=metadata_json_path
        )
        self.log(f"[SP PARSER AGENT] {sp_result}")
        if not sp_result.get("success"):
            if not self.guardrail_check(sp_result):
                return {"success": False, "message": "GuardRail blocked after SP Extraction."}
            return {"success": False, "message": "Failed at Stored Procedure Extraction."}

        # Step 4: File Validation (SP Schema)
        file_result_2 = self.file_agent.run(user_excel_path, sp_result.get("schema_path"))
        self.log(f"[FILE VALIDATION AGENT - SP Schema] {file_result_2}")
        if not file_result_2.get("success"):
            if not self.guardrail_check(file_result_2):
                return {"success": False, "message": "GuardRail blocked after SP File Validation."}
            return {"success": False, "message": "Failed at File Validation after SP Schema."}

        # Step 5: Load Data Validation Rules (After SP Extraction)
        data_validation_json_path = sp_result.get("data_json")
        self.data_validator.load_prompt("data_validation_prompts.txt")
        self.data_validator.load_rules(data_validation_json_path)

        # Step 6: Data Validation
        data_result = self.data_validator.run(
            user_excel_path,
            output_json_path="data/outputs/validation_results.json",
            output_excel_path="data/outputs/validation_results.xlsx"
        )
        self.log(f"[DATA VALIDATOR AGENT] {data_result}")
        if not data_result.get("success"):
            if not self.guardrail_check(data_result):
                return {"success": False, "message": "GuardRail blocked after Data Validation."}
            return {"success": False, "message": "Failed at Data Validation."}

        self.log("[MASTER] Multi-Agent Validation Flow Completed Successfully.")
        return {"success": True, "message": "All stages completed successfully."}
